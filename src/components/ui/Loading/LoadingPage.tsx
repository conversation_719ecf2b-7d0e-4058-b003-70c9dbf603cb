import { cn } from '@/utilities/cn'
import { usePathname } from 'next/navigation'
import React from 'react'
import { Spinner } from './Spinner'
interface LoadingPageProps extends React.HTMLAttributes<HTMLDivElement> {}
export const LoadingPage: React.FC<LoadingPageProps> = ({ className, ...props }) => {
  const pathName = usePathname()

  const isDesktopMode = pathName.includes('desktop')

  return (
    <div
      className={cn(
        `${isDesktopMode ? '' : 'mobile-wrapper'} fixed inset-0 z-[400] flex h-screen w-full select-none items-center justify-center bg-white/40`,
        className,
      )}
      {...props}
    >
      <div className="flex items-center justify-center space-x-2">
        <Spinner className="size-10"></Spinner>
      </div>
    </div>
  )
}
