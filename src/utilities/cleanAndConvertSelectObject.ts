import { isObject, transform } from 'lodash-es'

export function cleanAndConvertSelectObject(obj: any): any {
  if (!obj) return undefined

  return transform(
    obj,
    (result, value, key) => {
      if (isObject(value) && !Array.isArray(value)) {
        // Recursively handle nested objects
        result[key] = cleanAndConvertSelectObject(value)
      } else if (value === 'true') {
        result[key] = true
      } else if (value === 'false') {
        result[key] = false
      }
      // Keys with values other than "true", "false", or objects are ignored.
    },
    {} as any,
  )
}

export type SelectObject = { [key: string]: string | SelectObject }
