import { authenticatedWithValidSession } from '@/access/authenticatedWithValidSession'
import { LocaleEnum } from '@/enums/locale.enum'
import { withErrorHandling } from '@/utils/errorHandlers'
import { APIError, PayloadRequest, Where } from 'payload'

const getFavoriteKeywordByUserImpl = async (req: PayloadRequest) => {
  const isValid = await authenticatedWithValidSession({ req })

  if (!isValid) {
    throw new APIError('MES-448', 401)
  }

  const user = req.user!

  const { limit, page, where, locale, ...otherQuery } = req.query
  const customWhere = where ? (where as Where) : {}
  const data = await req.payload.find({
    collection: 'favorite-keywords',
    where: {
      and: [{ user: { equals: user.id }, ...customWhere }],
    },
    depth: 5,
    select: { keyword: true },
    sort: '-createdAt',
    limit: limit as number | undefined,
    page: page as number | 1,
    locale: locale as LocaleEnum | 'vi',
    ...otherQuery,
  })

  // Check if any keywords have relatedKeywords
  const relatedKeywordIds = new Set<string>()
  data.docs.forEach((doc) => {
    if (
      doc.keyword &&
      typeof doc.keyword === 'object' &&
      doc.keyword.relatedKeywords &&
      Array.isArray(doc.keyword.relatedKeywords)
    ) {
      doc.keyword.relatedKeywords.forEach((relatedKeyword) => {
        if (typeof relatedKeyword === 'object' && relatedKeyword.id) {
          relatedKeywordIds.add(relatedKeyword.id)
        } else if (typeof relatedKeyword === 'string') {
          relatedKeywordIds.add(relatedKeyword)
        }
      })
    }
  })

  // Get all favorite keywords for the user to check against
  const favorites = new Set<string>()

  // Add the main keywords to favorites set
  data.docs.forEach((doc) => {
    if (doc.keyword && typeof doc.keyword === 'object' && doc.keyword.id) {
      favorites.add(doc.keyword.id)
    }
  })

  // If there are related keywords, fetch additional favorites
  if (relatedKeywordIds.size > 0) {
    const additionalFavorites = await req.payload.find({
      collection: 'favorite-keywords',
      where: {
        and: [
          {
            'keyword.id': { in: Array.from(relatedKeywordIds) },
            'user.id': { equals: user.id },
          },
        ],
      },
      pagination: false,
      select: { keyword: true },
      depth: 0,
    })

    additionalFavorites.docs.forEach((doc) => {
      if (doc.keyword) {
        favorites.add(typeof doc.keyword === 'object' ? doc.keyword.id : doc.keyword)
      }
    })
  }

  // Add isFavorite field to each keyword and its related keywords
  if (data.docs && data.docs.length > 0) {
    data.docs = data.docs.map((doc) => {
      if (doc.keyword && typeof doc.keyword === 'object') {
        const updatedKeyword = {
          ...doc.keyword,
          isFavorite: true, // Main keywords are always favorites since they're from favorite-keywords collection
        }

        // Add isFavorite flag to related keywords if they exist
        if (updatedKeyword.relatedKeywords && Array.isArray(updatedKeyword.relatedKeywords)) {
          updatedKeyword.relatedKeywords = updatedKeyword.relatedKeywords.map((relatedKeyword) => {
            if (typeof relatedKeyword === 'object' && relatedKeyword.id) {
              return {
                ...relatedKeyword,
                isFavorite: favorites.has(relatedKeyword.id),
              }
            }
            return relatedKeyword
          })
        }

        return {
          ...doc,
          keyword: updatedKeyword,
        }
      }
      return doc
    })
  }

  return Response.json(data, { status: 200 })
}

// Wrap the handler with standardized error handling
export const getFavoriteKeywordByUserHandler = withErrorHandling(
  getFavoriteKeywordByUserImpl,
  'An unexpected error occurred while fetching favorite keywords',
)
