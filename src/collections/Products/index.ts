import type { CollectionConfig } from 'payload'

import { BlocksFeature, HeadingFeature, lexicalEditor } from '@payloadcms/richtext-lexical'

import { Banner } from '../../blocks/Banner/config'
import { Code } from '../../blocks/Code/config'
import { MediaBlock } from '../../blocks/MediaBlock/config'

import { admins } from '@/access/admins'

import { anyone } from '@/access/anyone'
import { AdsenseBlock } from '@/blocks/AdsenseBlock/config'
import { PRODUCT_V2_TYPE_OPTIONS_BASE } from '@/features/product-v2/constants/product-v2-type-options'
import { ProductV2TypeEnum } from '@/features/product-v2/enums'
import { slugField } from '@/fields/slug'
import {
  MetaDescriptionField,
  MetaImageField,
  MetaTitleField,
  OverviewField,
  PreviewField,
} from '@payloadcms/plugin-seo/fields'
import { getFilteredProductsHandler } from './handlers/getFilteredProducts'
import { getProductBySlugHandler } from './handlers/getProductBySlug'
import { deleteDifyDocumentAfterDeleteProduct } from './hooks/deleteDifyDocumentAfterDeleteProduct'
import { syncDifyDocumentAfterChangeProduct } from './hooks/syncDifyDocumentAfterChangeProduct'

export const Products: CollectionConfig = {
  slug: 'products',
  access: {
    create: admins,
    delete: admins,
    read: anyone,
    update: admins,
  },

  admin: {
    group: 'Products',
    defaultColumns: ['title', 'slug', 'updatedAt'],
    useAsTitle: 'title',
    listSearchableFields: ['title', 'id'],
  },

  fields: [
    {
      name: 'title',
      type: 'text',
      label: 'Name',
      required: true,
      index: true,
    },
    {
      name: 'jaTitle',
      type: 'text',
      label: 'Japanese Name',
      index: true,
    },
    {
      name: 'description',
      type: 'textarea',
    },
    {
      name: 'availableIn',
      label: 'Available In Subscriptions',
      type: 'relationship',
      relationTo: 'subscriptions',
      hasMany: true,
      required: true,
    },
    {
      name: 'type',
      type: 'select',
      options: Object.values(PRODUCT_V2_TYPE_OPTIONS_BASE).map((option) => ({
        label: option.label,
        value: option.value,
      })),
      required: true,
      defaultValue: ProductV2TypeEnum.MEDICINE,
    },
    {
      name: 'ageGroups',
      label: 'Age Groups',
      type: 'relationship',
      relationTo: 'product-age-groups',
      hasMany: true,
      required: true,
      admin: {
        condition: (data, siblingData) => {
          if (!data.type && !siblingData?.type) {
            return true
          }

          const currentType = data.type || siblingData?.type
          return (
            currentType === ProductV2TypeEnum.MEDICINE ||
            currentType === ProductV2TypeEnum.DIETARY_SUPPLEMENT
          )
        },
      },
    },
    {
      name: 'medicineType',
      label: 'Medicine Types',
      type: 'relationship',
      hasMany: true,
      relationTo: 'medicine-type',
      admin: {
        condition: (data, siblingData) => {
          if (!data.type && !siblingData?.type) {
            return true
          }

          const currentType = data.type || siblingData?.type
          return currentType === ProductV2TypeEnum.MEDICINE
        },
      },
    },
    {
      name: 'categories',
      type: 'relationship',
      hasMany: true,
      relationTo: 'product-categories',
      required: true,
      filterOptions: ({ data }) => {
        const productType = data.type
        if (!productType) return false
        return {
          type: {
            equals: productType,
          },
        }
      },
    },

    {
      name: 'featured',
      label: 'Featured',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'stores',
      type: 'array',
      admin: {
        position: 'sidebar',
      },
      fields: [
        {
          name: 'medicine-store',
          type: 'relationship',
          relationTo: 'medicine-buy-button',
        },
        {
          name: 'url',
          type: 'text',
        },
      ],
    },
    {
      type: 'tabs',
      tabs: [
        {
          fields: [
            {
              name: 'heroImage',
              type: 'upload',
              relationTo: 'media',
              required: true,
            },

            {
              name: 'uses',
              type: 'richText',
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [
                    ...rootFeatures,
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                    BlocksFeature({ blocks: [Banner, Code, MediaBlock, AdsenseBlock] }),
                  ]
                },
              }),
            },
            {
              name: 'dosageForm',
              type: 'richText',
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [
                    ...rootFeatures,
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                    BlocksFeature({ blocks: [Banner, Code, MediaBlock, AdsenseBlock] }),
                  ]
                },
              }),
            },
            {
              name: 'specification',
              type: 'richText',
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [
                    ...rootFeatures,
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                    BlocksFeature({ blocks: [Banner, Code, MediaBlock, AdsenseBlock] }),
                  ]
                },
              }),
            },
            {
              name: 'ingredient',
              type: 'richText',
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [
                    ...rootFeatures,
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                    BlocksFeature({ blocks: [Banner, Code, MediaBlock, AdsenseBlock] }),
                  ]
                },
              }),
            },
            {
              name: 'dosage',
              type: 'richText',
              editor: lexicalEditor({
                features: ({ rootFeatures, defaultFeatures }) => {
                  return [
                    ...rootFeatures,
                    ...defaultFeatures,
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                    BlocksFeature({ blocks: [Banner, Code, MediaBlock, AdsenseBlock] }),
                  ]
                },
              }),
            },
            {
              name: 'contraindications',
              type: 'richText',
              editor: lexicalEditor({
                features: ({ rootFeatures, defaultFeatures }) => {
                  return [
                    ...rootFeatures,
                    ...defaultFeatures,
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                    BlocksFeature({ blocks: [Banner, Code, MediaBlock, AdsenseBlock] }),
                  ]
                },
              }),
            },
            {
              name: 'note',
              type: 'richText',
              editor: lexicalEditor({
                features: ({ rootFeatures, defaultFeatures }) => {
                  return [
                    ...rootFeatures,
                    ...defaultFeatures,
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                    BlocksFeature({ blocks: [Banner, Code, MediaBlock, AdsenseBlock] }),
                  ]
                },
              }),
            },
            {
              name: 'keywords',
              type: 'relationship',
              relationTo: 'keywords',
              hasMany: true,
            },
          ],
          label: 'Content',
        },
        {
          fields: [
            {
              name: 'relatedProducts',
              type: 'relationship',
              admin: {
                position: 'sidebar',
              },
              filterOptions: ({ id }) => {
                return {
                  id: {
                    not_in: [id],
                  },
                }
              },
              hasMany: true,
              relationTo: 'products',
            },
          ],
          label: 'Meta',
        },
        {
          name: 'meta',
          label: 'SEO',
          fields: [
            OverviewField({
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
              imagePath: 'meta.image',
            }),
            MetaTitleField({
              hasGenerateFn: true,
            }),
            MetaImageField({
              relationTo: 'media',
            }),

            MetaDescriptionField({}),
            PreviewField({
              // if the `generateUrl` function is configured
              hasGenerateFn: true,

              // field paths to match the target field for data
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
            }),
          ],
        },
      ],
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
        position: 'sidebar',
      },
      hooks: {
        // beforeChange: [
        //   ({ siblingData, value }) => {
        //     if (siblingData._status === 'published' && !value) {
        //       return new Date()
        //     }
        //     return value
        //   },
        // ],
      },
    },
    {
      name: 'unverified',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'AIAnalyzed',
      type: 'richText',
      admin: {
        position: 'sidebar',
      },
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [
            ...rootFeatures,
            HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
            BlocksFeature({ blocks: [Banner, Code, MediaBlock, AdsenseBlock] }),
          ]
        },
      }),
    },

    ...slugField(),
    {
      name: 'difyDocumentId',
      type: 'text',
    },
    {
      name: 'mergedText',
      type: 'text',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'triggerDifyDocumentSync',
      type: 'checkbox',
      admin: {
        position: 'sidebar',
        description: 'Use this field to trigger Dify document sync (support bulk edit)',
      },
    },
  ],
  hooks: {
    afterChange: [syncDifyDocumentAfterChangeProduct],
    afterDelete: [deleteDifyDocumentAfterDeleteProduct],
  },
  endpoints: [
    {
      method: 'get',
      path: '/filtered',
      handler: getFilteredProductsHandler,
    },
    {
      method: 'get',
      path: '/details/:slug',
      handler: getProductBySlugHandler,
    },
  ],

  // versions: {
  //   drafts: {
  //     autosave: {
  //       interval: 4000,
  //     },
  //   },
  //   maxPerDoc: 50,
  // },
  //   hooks: {
  //     afterChange: [syncDifyDocumentAfterChangeMedicine],
  //     afterDelete: [deleteDifyDocumentAfterDeleteMedicine],
  //   },
  //   endpoints: [
  //     {
  //       method: 'get',
  //       path: '/v2/list',
  //       handler: getMedicinesV2Handler,
  //     },
  //     {
  //       method: 'get',
  //       path: '/v2/detail/:slug',
  //       handler: getMedicineBySlugV2Handler,
  //     },
  //   ],
}
