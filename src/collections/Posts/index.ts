import type { CollectionConfig } from 'payload'

import {
  BlocksFeature,
  FixedToolbarFeature,
  HeadingFeature,
  HorizontalRuleFeature,
  InlineToolbarFeature,
  lexicalEditor,
} from '@payloadcms/richtext-lexical'

import { Banner } from '../../blocks/Banner/config'
import { Code } from '../../blocks/Code/config'
import { MediaBlock } from '../../blocks/MediaBlock/config'
import { generatePreviewPath } from '../../utilities/generatePreviewPath'
import { populateAuthors } from './hooks/populateAuthors'
import { revalidateDelete, revalidatePost } from './hooks/revalidatePost'

import { admins } from '@/access/admins'
import { slugField } from '@/fields/slug'
import {
  MetaDescriptionField,
  MetaImageField,
  MetaTitleField,
  OverviewField,
  PreviewField,
} from '@payloadcms/plugin-seo/fields'
import { filterPostsCategoryHandler } from './handlers/filterPostsCategoryHandler'

import { AdsenseBlock } from '@/blocks/AdsenseBlock/config'
import { LOCALE_MAP_I18N } from '@/constants/locale.constant'
import { LocaleEnum } from '@/enums/locale.enum'
import { getPostsByLangHandler } from './handlers/getPostsByLangHandler'
import { deleteDifyDocumentAfterDeletePost } from './hooks/deleteDifyDocumentAfterDeletePost'
import { syncDifyDocumentAfterChangePost } from './hooks/syncDifyDocumentAfterChangePost'
import { anyone } from '@/access/anyone'

export const Posts: CollectionConfig<'posts'> = {
  slug: 'posts',
  access: {
    create: admins,
    delete: admins,
    read: anyone,
    update: admins,
  },

  // This config controls what's populated by default when a post is referenced
  // https://payloadcms.com/docs/queries/select#defaultpopulate-collection-config-property
  // Type safe if the collection slug generic is passed to `CollectionConfig` - `CollectionConfig<'posts'>
  defaultPopulate: {
    title: true,
    slug: true,
    categories: true,
    heroImage: true,
    meta: {
      image: true,
      description: true,
    },
    createdAt: true,
  },
  admin: {
    group: 'Posts',
    defaultColumns: ['title', 'slug', 'updatedAt'],
    livePreview: {
      url: ({ data, req }) => {
        const path = generatePreviewPath({
          slug: typeof data?.slug === 'string' ? data.slug : '',
          collection: 'posts',
          req,
        })

        return path
      },
    },
    preview: (data, { req }) =>
      generatePreviewPath({
        slug: typeof data?.slug === 'string' ? data.slug : '',
        collection: 'posts',
        req,
      }),
    useAsTitle: 'title',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      index: true,
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'language',
      type: 'select',
      options: [
        // {
        //   label: 'English',
        //   value: 'en',
        // },
        {
          label: LOCALE_MAP_I18N[LocaleEnum.VI].fixedLabel,
          value: LocaleEnum.VI,
        },
        // {
        //   label: 'Chinese',
        //   value: 'cn',
        // },
        {
          label: LOCALE_MAP_I18N[LocaleEnum.JA].fixedLabel,
          value: LocaleEnum.JA,
        },
      ],
      defaultValue: undefined,
      // required: true,
    },
    {
      type: 'tabs',
      tabs: [
        {
          fields: [
            {
              name: 'heroImage',
              type: 'upload',
              relationTo: 'media',
            },
            {
              name: 'content',
              label: 'Content',
              type: 'richText',
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [
                    ...rootFeatures,
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                    BlocksFeature({ blocks: [Banner, Code, MediaBlock, AdsenseBlock] }),
                    FixedToolbarFeature(),
                    InlineToolbarFeature(),
                    HorizontalRuleFeature(),
                  ]
                },
              }),

              required: true,
            },
            // {
            //   name: 'keywords',
            //   type: 'array',
            //   localized: true,
            //   fields: [
            //     {
            //       name: 'keyword',
            //       type: 'text',
            //     },
            //   ],
            // },
            {
              name: 'relatedKeywords',
              type: 'relationship',
              relationTo: 'keywords',
              hasMany: true,
            },
          ],
          label: 'Content',
        },
        {
          fields: [
            {
              name: 'relatedPosts',
              type: 'relationship',
              admin: {
                position: 'sidebar',
              },
              filterOptions: ({ id }) => {
                return {
                  id: {
                    not_in: [id],
                  },
                }
              },
              hasMany: true,
              relationTo: 'posts',
            },
          ],
          label: 'Meta',
        },
        {
          name: 'meta',
          label: 'SEO',
          fields: [
            OverviewField({
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
              imagePath: 'meta.image',
            }),
            MetaTitleField({
              hasGenerateFn: true,
            }),
            MetaImageField({
              relationTo: 'media',
            }),

            MetaDescriptionField({}),
            PreviewField({
              // if the `generateUrl` function is configured
              hasGenerateFn: true,

              // field paths to match the target field for data
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
            }),
          ],
        },
      ],
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
        position: 'sidebar',
      },
      hooks: {
        beforeChange: [
          ({ siblingData, value }) => {
            if (siblingData._status === 'published' && !value) {
              return new Date()
            }
            return value
          },
        ],
      },
    },
    {
      name: 'authors',
      type: 'relationship',
      admin: {
        position: 'sidebar',
      },
      hasMany: true,
      relationTo: 'users',
    },
    // This field is only used to populate the user data via the `populateAuthors` hook
    // This is because the `user` collection has access control locked to protect user privacy
    // GraphQL will also not return mutated user data that differs from the underlying schema
    {
      name: 'populatedAuthors',
      type: 'array',
      access: {
        update: () => false,
      },
      admin: {
        disabled: true,
        readOnly: true,
      },
      fields: [
        {
          name: 'id',
          type: 'text',
        },
        {
          name: 'name',
          type: 'text',
        },
      ],
    },
    {
      name: 'categories',
      type: 'relationship',
      admin: {
        position: 'sidebar',
      },
      relationTo: 'post-categories',
    },
    ...slugField(),
    {
      name: 'mergedText',
      type: 'textarea',
      admin: {
        readOnly: true,
        description:
          'This field is automatically populated from post title and post content editor',
      },
    },
    {
      name: 'difyDocumentId',
      type: 'text',
      admin: {
        description: 'Dify document ID for this post',
      },
    },
    {
      name: 'triggerDifyDocumentSync',
      type: 'checkbox',
      admin: {
        position: 'sidebar',
        description: 'Use this field to trigger Dify document sync (support bulk edit)',
      },
    },
  ],
  hooks: {
    afterChange: [revalidatePost, syncDifyDocumentAfterChangePost],
    afterRead: [populateAuthors],
    afterDelete: [revalidateDelete, deleteDifyDocumentAfterDeletePost],
  },
  // versions: {
  //   drafts: {
  //     autosave: {
  //       interval: 10000, // We set this interval for optimal live preview
  //     },
  //   },
  //   maxPerDoc: 20,
  // },
  endpoints: [
    {
      method: 'get',
      path: '/filter-categories',
      handler: filterPostsCategoryHandler,
    },
    {
      method: 'get',
      path: '/lang/:lang',
      handler: getPostsByLangHandler,
    },
  ],
}
