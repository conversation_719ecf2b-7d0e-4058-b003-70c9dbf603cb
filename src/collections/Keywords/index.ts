import { admins } from '@/access/admins'
import { anyone } from '@/access/anyone'
import { Banner } from '@/blocks/Banner/config'
import { Code } from '@/blocks/Code/config'
import { MediaBlock } from '@/blocks/MediaBlock/config'
import { keywordCategoryOptions } from '@/constants/keywordList.constant'
import {
  BlocksFeature,
  FixedToolbarFeature,
  HeadingFeature,
  HorizontalRuleFeature,
  InlineToolbarFeature,
  lexicalEditor,
} from '@payloadcms/richtext-lexical'
import { CollectionConfig } from 'payload'
import { checkKeywordExistHandler } from './handlers/checkKeywordExistHandler'
import { createMultipleKeywordsHandler } from './handlers/createMultipleKeywordsHandler'
import { fullTextSearchKeywordsHandler } from './handlers/fullTextSearchKeywordsHandler'
import { getListKeywordsHandler } from './handlers/getListKeywordsHandler'
import { getRandomKeywordsHandler } from './handlers/getRandomKeywordsHandler'
import { keywordTextToSpeechHandler } from './handlers/keywordTextToSpeechHandler'
import { getPopulatedSearchKeywordsHandler } from './handlers/getPopulatedSearchKeywords'
import { covertDescriptionToHTMLAfterReadHook } from './hooks/covertDescriptionToHTMLAfterReadHook'
import { syncDifyDocumentAfterChangeKeyword } from './hooks/syncDifyDocumentAfterChangeKeyword'
import { deleteDifyDocumentAfterDeleteKeyword } from './hooks/deleteDifyDocumentAfterDeleteKeyword'
import { getKeywordAudioHandler } from './handlers/getKeywordAudioHandler'
import { getKeywordDetailsHandler } from './handlers/getKeywordDetailsHandler'

export const Keywords: CollectionConfig = {
  slug: 'keywords',
  access: {
    read: anyone,
    create: admins,
    update: admins,
    delete: admins,
  },
  admin: {
    group: 'Handbooks',
    useAsTitle: 'name',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      maxLength: 256,
      required: true,
      localized: true,
      unique: true,
      index: true,
    },
    {
      name: 'hiragana',
      type: 'text',
      label: 'Furigana',
      maxLength: 256,
      required: true,
      unique: true,
      // access: {
      //   read: adminOrAdvancedAccess,
      //   create: admins,
      //   update: admins,
      // },
    },
    // Có thể thêm các giá trị vào trường thông tin ví dụ "3 ngày"
    {
      name: 'editable',
      type: 'checkbox',
      label: 'Allow to add custom value?',
      defaultValue: false,
      admin: {
        position: 'sidebar',
      },
    },
    // Thể hiện giá trị "Khác"
    {
      name: 'isCustom',
      type: 'checkbox',
      label: 'Has another option?',
      defaultValue: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'popupType',
      type: 'select',
      label: 'Pop-up type',
      options: [
        {
          label: 'Day',
          value: 'day',
        },
        {
          label: 'Week',
          value: 'week',
        },
        {
          label: 'Month',
          value: 'month',
        },
        {
          label: 'Year',
          value: 'year',
        },
        {
          label: 'Other',
          value: 'other',
        },
        {
          label: 'Keyword Description',
          value: 'description',
        },
        {
          label: 'Fever temperature',
          value: 'fever',
        },
      ],
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'description',
      type: 'richText',
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [
            ...rootFeatures,
            HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
            BlocksFeature({ blocks: [Banner, Code, MediaBlock] }),
            FixedToolbarFeature(),
            InlineToolbarFeature(),
            HorizontalRuleFeature(),
          ]
        },
      }),
      label: 'Description',
    },
    {
      name: 'unverified',
      label: 'Unverified',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'categories',
      type: 'select',
      label: 'Categories',
      options: keywordCategoryOptions,
      hasMany: true,
      index: true,
      admin: {
        description: 'Select categories this keyword belongs to',
      },
    },
    {
      name: 'relatedKeywords',
      type: 'relationship',
      relationTo: 'keywords',
      hasMany: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'relatedImages',
      type: 'upload',
      relationTo: 'media',
      hasMany: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'audio',
      type: 'array',
      fields: [
        {
          name: 'language',
          type: 'select',
          options: ['vi', 'ja'],
        },
        {
          name: 'audio',
          type: 'upload',
          relationTo: 'media',
        },
      ],
    },
    {
      name: 'searchCount',
      type: 'number',
      defaultValue: 0,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'mergedText',
      type: 'text',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'difyDocumentId',
      type: 'text',
      admin: {
        readOnly: true,
      },
    },
  ],
  hooks: {
    afterRead: [covertDescriptionToHTMLAfterReadHook],
    // afterChange: [syncDifyDocumentAfterChangeKeyword],
    // afterDelete: [deleteDifyDocumentAfterDeleteKeyword],
  },
  endpoints: [
    {
      path: '/create-multiple',
      method: 'post',
      handler: createMultipleKeywordsHandler,
    },
    {
      path: '/list',
      method: 'get',
      handler: getListKeywordsHandler,
    },
    {
      path: '/audio',
      method: 'post',
      handler: keywordTextToSpeechHandler,
    },
    {
      path: '/full-text-search',
      method: 'get',
      handler: fullTextSearchKeywordsHandler,
    },
    {
      path: '/random',
      method: 'get',
      handler: getRandomKeywordsHandler,
    },
    {
      path: '/check-exist',
      method: 'get',
      handler: checkKeywordExistHandler,
    },
    {
      path: '/populated-search',
      method: 'get',
      handler: getPopulatedSearchKeywordsHandler,
    },
    {
      path: '/audio/:keywordId',
      method: 'get',
      handler: getKeywordAudioHandler,
    },
    {
      path: '/details/:id',
      method: 'get',
      handler: getKeywordDetailsHandler,
    },
  ],
}
