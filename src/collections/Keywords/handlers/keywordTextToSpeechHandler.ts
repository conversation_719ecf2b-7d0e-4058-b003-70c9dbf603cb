import { addDataAndFileToRequest } from 'payload'
import { PayloadRequest } from 'payload'
import { generateKeywordAudio } from '../utils/keywordAudioHandler'

export const keywordTextToSpeechHandler = async (req: PayloadRequest) => {
  if (!req || !req.json || !req.routeParams) {
    return Response.json(
      {
        error: 'Request or route parameters are missing.',
      },
      { status: 400 },
    )
  }

  // Process and attach data and file to the request object
  await addDataAndFileToRequest(req)

  const { keywordId, language: audioLanguage } = req.data || {}
  const language = audioLanguage || req.locale || 'vi'

  if (!keywordId) {
    return Response.json({ error: 'No keywordId provided' }, { status: 400 })
  }

  const result = await generateKeywordAudio({
    keywordId: keywordId as string,
    language,
    req,
  })

  return Response.json(result, { status: 200 })
}
