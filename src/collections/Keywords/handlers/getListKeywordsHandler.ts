import { PayloadRequest, Where } from 'payload'

import { LocaleEnum } from '@/enums/locale.enum'
import { withErrorHandling } from '@/utils/errorHandlers'
import { transform } from 'lodash-es'

export const getListKeywordsImpl = async (req: PayloadRequest) => {
  const user = req?.user
  const queries = req.query
  const { where, select, populate, locale, withFavoriteFlag, ...otherQueries } = queries
  const { or, and, ...rest } = (where as Where) || {}
  const dynamicWhere: Where = {
    ...rest,
    ...(or && { or }),
    ...(and && { and }),
  }

  const customWhere: Where = {
    unverified: {
      equals: false,
    },
  }

  const finalWhere: Where = {
    ...dynamicWhere,
    ...customWhere,
  }

  const selectObj = select ? cleanAndConvertSelectObject(select as SelectObject) : undefined
  const hasRelatedKeywords = selectObj && selectObj.relatedKeywords === true

  const data = await req.payload.find({
    collection: 'keywords',
    sort: 'createdAt',
    locale: (locale as LocaleEnum) || LocaleEnum.VI,
    ...otherQueries,
    where: finalWhere,
    populate: cleanAndConvertSelectObject(populate as SelectObject),
    select: {
      id: true,
      name: true,
      ...(selectObj || {}),
    },
  })

  const medicineIds = data.docs.map((m) => m.id)

  if (withFavoriteFlag === 'true' && user) {
    const relatedKeywordIds = new Set<string>()
    if (hasRelatedKeywords) {
      data.docs.forEach((doc) => {
        if (doc.relatedKeywords && Array.isArray(doc.relatedKeywords)) {
          doc.relatedKeywords.forEach((relatedKeyword) => {
            if (typeof relatedKeyword === 'object' && relatedKeyword.id) {
              relatedKeywordIds.add(relatedKeyword.id)
            } else if (typeof relatedKeyword === 'string') {
              relatedKeywordIds.add(relatedKeyword)
            }
          })
        }
      })
    }

    const allKeywordIds = [...medicineIds, ...Array.from(relatedKeywordIds)]

    const currentFavorites = await req.payload.find({
      collection: 'favorite-keywords',
      where: {
        and: [
          {
            'keyword.id': { in: allKeywordIds },
            'user.id': { equals: user?.id },
          },
        ],
      },
      pagination: false,
      select: { keyword: true },
      depth: 0,
    })

    const favorites = new Set(currentFavorites.docs.map((d) => d.keyword as string))

    data.docs = data.docs.map((doc) => {
      const updatedDoc = {
        ...doc,
        isFavorite: favorites.has(doc.id),
      }

      // Add isFavorite flag to related keywords if they exist
      if (hasRelatedKeywords && doc.relatedKeywords && Array.isArray(doc.relatedKeywords)) {
        updatedDoc.relatedKeywords = doc.relatedKeywords.map((relatedKeyword) => {
          if (typeof relatedKeyword === 'object' && relatedKeyword.id) {
            return {
              ...relatedKeyword,
              isFavorite: favorites.has(relatedKeyword.id),
            }
          }
          return relatedKeyword
        })
      }

      return updatedDoc
    })
  }

  return Response.json(data || null)
}

export const getListKeywordsHandler = withErrorHandling(
  getListKeywordsImpl,
  'An unexpected error occurred while fetching keywords',
)

export function cleanAndConvertSelectObject(obj: { [key: string]: string }): {
  [key: string]: boolean
} {
  return transform(
    obj,
    (result, value, key) => {
      if (value === 'true') {
        result[key] = true
      } else if (value === 'false') {
        result[key] = false
      }
      // Keys with values other than "true" or "false" are ignored.
    },
    {},
  )
}
export type SelectObject = { [key: string]: string }
