// storage-adapter-import-placeholder
import { mongooseAdapter } from '@payloadcms/db-mongodb'

import path from 'path'
import { buildConfig } from 'payload'
import sharp from 'sharp' // sharp-import
import { fileURLToPath } from 'url'

import { defaultLexical } from '@/fields/defaultLexical'
import { Footer } from './Footer/config'
import { Header } from './Header/config'
import { Categories } from './collections/Categories'
import { Media } from './collections/Media/Media'
import { Pages } from './collections/Pages'
import { Posts } from './collections/Posts'
import { Users } from './collections/Users'
import { plugins } from './plugins'

// import { en } from '@payloadcms/translations/languages/en'
import { ja } from '@payloadcms/translations/languages/ja'
import { vi } from '@payloadcms/translations/languages/vi'
// import { zh } from '@payloadcms/translations/languages/zh'

import { BodyParts } from '@/collections/BodyParts'
import { EmailSubscriptions } from '@/collections/EmailSubscriptions'
import { Faculties } from '@/collections/Faculties'
import { KOLs } from '@/collections/KOL'
import { Keywords } from '@/collections/Keywords'
import { LoginSessions } from '@/collections/LoginSessions'
import { PaymentMethods } from '@/collections/PaymentMethods'
import { Questions } from '@/collections/Questions'
import { Subscriptions } from '@/collections/Subscriptions'
import { Summarizes } from '@/collections/Summarizes'
import { Symptoms } from '@/collections/Symptoms'
import { Transactions } from '@/collections/Transactions'
import { UserSearchHistories } from '@/collections/UserSearchHistories'
import { Versions } from '@/collections/Versions'
import { Campaigns } from '@/collections/campaign'
import { Coupons } from '@/collections/coupon'
import { getAllowOrigins } from '@/utilities/getAllowOrigins'
import { getServerSideURL } from '@/utilities/getURL'
import { nodemailerAdapter } from '@payloadcms/email-nodemailer'
import { sentryPlugin } from '@payloadcms/plugin-sentry'
import * as Sentry from '@sentry/nextjs'
import { FavoriteMedicines } from 'src/collections/FavoriteMedicines'
import { CouponTracking } from './collections/CouponTracking'
import { DietarySupplementCategories } from './collections/DietarySupplementCategories'
import { DynamicPopups } from './collections/DynamicPopups'
import { ExaminationForms } from './collections/ExaminationForms'
import { ExaminationQuestions } from './collections/ExaminationQuestions'
import { Facts } from './collections/Facts'
import { FavoriteKeywords } from './collections/FavoriteKeywords'
import { ImageSearchConversations } from './collections/ImageSearchConversations'
import { ImageSearchMessages } from './collections/ImageSearchMessages'
import { Medicine } from './collections/Medicine'
import { MedicineBuyButton } from './collections/MedicineBuyButton'
import { MedicineCategories } from './collections/MedicineCategories'
import { MedicineType } from './collections/MedicineType'
import { PatientGroup } from './collections/PatientGroup'
import { PopupTracking } from './collections/PopupTracking'
import { PostCategories } from './collections/PostCategories'
import { SearchTips } from './collections/SearchTips'
import UserExaminations from './collections/UserExaminations'
import UserSubscriptions from './collections/UserSubscriptions'
import { MAX_FILE_SIZE } from './constants/global.constant'
import { LOCALE_MAP } from './constants/locale.constant'
import { jobs } from './crons'
import { AI_BOT_ENDPOINTS } from './customAPI/ai-bot'
import { AIBotConfigs } from './globals/AIBotConfigs'
import { HomePageConfig } from './globals/HomePage'
import { X_LOGIN_SESSION } from './constants/storageKeys.constant'
import { Livestreams } from './collections/Livestreams'
import { SupportRequests } from './collections/SupportRequests'
import { Products } from './collections/Products'
import { ProductAgeGroups } from './collections/ProductAgeGroups'
import { ProductCategories } from './collections/ProductCategories'
import { FavoriteProducts } from './collections/FavoriteProducts'
import { KeywordSearchHistory } from './collections/KeywordSearchHistory'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  admin: {
    components: {
      // The `BeforeLogin` component renders a message that you see while logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below and the import `BeforeLogin` statement on line 15.
      beforeLogin: ['@/components/BeforeLogin'],
      // The `BeforeDashboard` component renders the 'welcome' block that you see after logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below and the import `BeforeDashboard` statement on line 15.
    },
    importMap: {
      baseDir: path.resolve(dirname),
    },
    user: Users.slug,
    livePreview: {
      breakpoints: [
        {
          label: 'Mobile',
          name: 'mobile',
          width: 375,
          height: 667,
        },
        {
          label: 'Tablet',
          name: 'tablet',
          width: 768,
          height: 1024,
        },
        {
          label: 'Desktop',
          name: 'desktop',
          width: 1440,
          height: 900,
        },
      ],
    },
    meta: {
      title: 'WAP Admin Panel',
      description: 'WAP Admin Panel',
      icons: [
        {
          rel: 'icon',
          type: 'image/png',
          url: '/favicon.ico',
        },
      ],
    },
  },

  // This config helps us configure global or default features that the other editors can inherit
  editor: defaultLexical,
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || '',
  }),
  collections: [
    Pages,
    Posts,
    Media,
    Categories,
    Users,
    PostCategories,
    Medicine,
    MedicineCategories,
    MedicineBuyButton,
    Coupons,
    KOLs,
    Campaigns,
    FavoriteMedicines,
    Subscriptions,
    LoginSessions,
    Versions,
    Symptoms,
    Keywords,
    Summarizes,
    Questions,
    Faculties,
    BodyParts,
    UserSearchHistories,
    EmailSubscriptions,
    UserSubscriptions,
    Transactions,
    PaymentMethods,
    PatientGroup,
    MedicineType,
    ExaminationForms,
    ExaminationQuestions,
    UserExaminations,
    DietarySupplementCategories,
    FavoriteKeywords,
    ImageSearchConversations,
    ImageSearchMessages,
    DynamicPopups,
    PopupTracking,
    CouponTracking,
    Facts,
    SearchTips,
    Livestreams,
    SupportRequests,
    Products,
    ProductAgeGroups,
    ProductCategories,
    FavoriteProducts,
    KeywordSearchHistory,
  ],
  csrf: [...getAllowOrigins()],
  cors: {
    origins: [...getAllowOrigins()],
    headers: ['Content-Type', 'Authorization', 'Referrer-Policy', X_LOGIN_SESSION],
  },
  serverURL: getServerSideURL(),
  globals: [Header, Footer, HomePageConfig, AIBotConfigs],
  plugins: [
    ...plugins,
    sentryPlugin({
      options: {
        captureErrors: [400, 403, 500],
        context: ({ defaultContext, req }) => {
          return {
            ...defaultContext,
            tags: {
              locale: req.locale,
            },
          }
        },
        debug: true,
      },
      Sentry,
    }),
    // storage-adapter-placeholder
  ],
  secret: process.env.PAYLOAD_SECRET,
  sharp,
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  i18n: {
    fallbackLanguage: 'vi',
    supportedLanguages: { ja, vi },
  },
  localization: {
    locales: Object.values(LOCALE_MAP),
    defaultLocale: LOCALE_MAP.vi.code,
    fallback: true,
  },
  email: nodemailerAdapter({
    defaultFromAddress: '<EMAIL>',
    defaultFromName: 'WAP',
    transportOptions: {
      host: process.env.SMTP_HOST,
      port: 587,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    },
  }),
  hooks: {
    // afterError: [
    //   async ({ error }) => {
    //     console.log(error.message)
    //     return Response.json({ error: error.message }, { status: 400 })
    //   },
    // ],
  },
  cookiePrefix: 'wap-portal',
  upload: {
    limits: {
      fileSize: MAX_FILE_SIZE, // 10MB, written in bytes
    },
  },
  jobs: {
    ...jobs,
  },
  endpoints: [...AI_BOT_ENDPOINTS],
  onInit: async (payload) => {
    payload.logger.info('👍 PayloadCMS initialized')

    // Check if the conversation reset job already exists
    const existingResetJob = await payload.find({
      collection: 'payload-jobs',
      where: {
        workflowSlug: {
          equals: 'resetConversationLimitWorkflow',
        },
      },
      limit: 1,
    })

    // Queue the conversation reset job if it doesn't exist
    if (!existingResetJob || existingResetJob.docs.length === 0) {
      payload.jobs.queue({
        workflow: 'resetConversationLimitWorkflow',
        queue: 'nightly',
        input: {},
      })
      payload.logger.info('✅ Conversation limit reset job queued successfully')
    } else {
      payload.logger.info('⚡ Conversation limit reset job already exists, skipping queue')
    }

    await payload.jobs.run()
  },
})
