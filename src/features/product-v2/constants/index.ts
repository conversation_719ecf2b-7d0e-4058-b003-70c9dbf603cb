import dietarySupplementIcon from '@/assets/icons/product-type-dietary-supplement.svg'
import medicalInstrumentIcon from '@/assets/icons/product-type-medical-instrument-icon.svg'
import medicineIcon from '@/assets/icons/product-type-medicine-icon.svg'
import { ProductV2TypeEnum } from '../enums'
import { PRODUCT_V2_TYPE_OPTIONS_BASE } from './product-v2-type-options'

export const PRODUCT_V2_TYPE_OPTIONS = {
  [ProductV2TypeEnum.MEDICINE]: {
    ...PRODUCT_V2_TYPE_OPTIONS_BASE[ProductV2TypeEnum.MEDICINE],
    icon: medicineIcon,
  },
  [ProductV2TypeEnum.DIETARY_SUPPLEMENT]: {
    ...PRODUCT_V2_TYPE_OPTIONS_BASE[ProductV2TypeEnum.DIETARY_SUPPLEMENT],
    icon: dietarySupplementIcon,
  },
  [ProductV2TypeEnum.MEDICAL_INSTRUMENT]: {
    ...PRODUCT_V2_TYPE_OPTIONS_BASE[ProductV2TypeEnum.MEDICAL_INSTRUMENT],
    icon: medicalInstrumentIcon,
  },
}

export const PRODUCT_V2_TYPE_OPTIONS_WITHOUT_ICON = PRODUCT_V2_TYPE_OPTIONS_BASE
