import SearchHeader from '../SearchHeader/SearchHeader'
import SearchKeywords from '../SearchKeywords/SearchKeywords'
import SearchTabs from '../SearchTabs/SearchTabs'

type SearchWrapperProps = {
  paramsValue: {
    [key: string]: string | undefined
  }
}
const SearchWrapper: React.FC<SearchWrapperProps> = ({ paramsValue }) => {
  return (
    <div className="bg-custom-background-hover px-16 py-6">
      <div className="rounded-3xl bg-white p-4">
        <SearchHeader paramsValue={paramsValue} />
        <SearchTabs />

        <SearchKeywords />
      </div>
    </div>
  )
}

export default SearchWrapper
