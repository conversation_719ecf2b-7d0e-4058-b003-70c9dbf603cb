import { Keyword } from '@/payload-types'
import { LocalizeField } from '@/types/custom-payload.type'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import SearchPopover from '../SearchPopover/SearchPopover'
import { useGetInfiniteKeywords } from '@/hooks/query/keyword/useGetInfiniteKeywords'
import InfiniteScroll from 'react-infinite-scroll-component'
import React from 'react'

import archiveIcon from '@/assets/icons/archive-add.svg'
import { LocaleEnum } from '@/enums/locale.enum'

type SearchKeywordsProps = {
  isViewAll?: boolean
}

const SearchKeywords: React.FC<SearchKeywordsProps> = ({ isViewAll = false }) => {
  const t = useTranslations()

  const { keywords, fetchNextPage, hasNextPage, isGetKeywordsLoading } = useGetInfiniteKeywords({
    params: {
      limit: isViewAll ? 20 : 6,
      locale: 'all',
    },
    config: {
      enabled: true,
    },
  })

  const flatItems = keywords?.pages.flatMap((page) => page?.docs).filter((item) => !!item) || []
  const totalCount = keywords?.pages[0]?.totalDocs || 0

  // For non-viewAll mode, only show first page items
  const displayItems = isViewAll ? flatItems : flatItems.slice(0, 6)

  const renderKeywordGrid = () => (
    <div className="mt-3 grid grid-cols-3 gap-3">
      {displayItems.map((item, index) => {
        const localizedName = item?.name as unknown as LocalizeField<string>
        return (
          <div
            key={`${item.id}-${index}`}
            className="flex min-w-[200px] flex-1 shrink-0 snap-start flex-col gap-2 rounded-lg bg-primary-50 p-4"
          >
            <div className="flex items-center justify-between gap-x-2">
              <div className="flex items-center gap-3">
                <p className="typo-body-2 text-primary-500"> {localizedName?.[LocaleEnum.JA]}</p>
                {item?.hiragana && <p className="typo-body-7 text-subdued">/{item?.hiragana}/</p>}
              </div>
              <div className="size-5 shrink-0 cursor-pointer self-start">
                <Image src={archiveIcon} alt="audio" width={20} height={20} className="size-5" />
              </div>
            </div>
            <p className="typo-body-7">{localizedName?.[LocaleEnum.VI]}</p>
          </div>
        )
      })}
    </div>
  )

  return (
    <div className="mt-3">
      <div className="typo-body-3 flex items-center gap-3">
        {t('MES-565')} ({totalCount})
        <SearchPopover tooltipContent={t('MES-568')} />
      </div>

      {isViewAll ? (
        <InfiniteScroll
          dataLength={flatItems.length}
          next={() => {
            if (hasNextPage) {
              fetchNextPage?.()
            }
          }}
          hasMore={!!hasNextPage}
          loader={<div className="flex justify-center py-4">Loading...</div>}
          scrollThreshold={0.8}
        >
          {renderKeywordGrid()}
        </InfiniteScroll>
      ) : (
        renderKeywordGrid()
      )}
    </div>
  )
}

export default SearchKeywords
