'use client'

import { useTranslations } from 'next-intl'
import { useState } from 'react'

const TABS = [
  {
    value: 'summary',
    label: 'MES-141',
    type: 'summary',
  },
  {
    value: 'HICO-BOT',
    label: 'MES-639',
    type: 'bot',
  },
  {
    value: 'faculties',
    label: 'MES-564',
    type: 'faculties',
  },
  {
    value: 'keywords',
    label: 'MES-565',
    type: 'keywords',
  },
  {
    value: 'products',
    label: 'MES-567',
    type: 'products',
  },
]

const SearchTabs: React.FC = () => {
  const t = useTranslations()

  const [activeTab, setActiveTab] = useState(TABS[0].value)
  return (
    <>
      <div className="typo-heading-7 mt-3">{t('MES-71')} (2)</div>

      <div className="mt-4 flex items-center gap-2">
        {TABS.map((tab) => (
          <div
            key={tab.value}
            onClick={() => setActiveTab(tab.value)}
            className={`typo-body-6 cursor-pointer rounded-xl border px-3 py-[6px] ${
              activeTab === tab.value
                ? 'border-primary-500 bg-white text-primary-500'
                : 'border-transparent bg-neutral-100 text-subdued'
            }`}
          >
            {t(tab.label)}
          </div>
        ))}
      </div>
    </>
  )
}

export default SearchTabs
