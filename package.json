{"name": "payload-cms", "version": "1.0.0", "description": "Website template for Payload", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation NODE_OPTIONS='--max-old-space-size=8192' next build", "turbobuild": "cross-env NODE_OPTIONS=--no-deprecation NODE_OPTIONS='--max-old-space-size=8192' next build --turbopack", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "turbo": "cross-env NODE_OPTIONS=--no-deprecation next dev --turbopack", "dev:prod": "cross-env NODE_OPTIONS=--no-deprecation rm -rf .next && pnpm build && pnpm start", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "ii": "cross-env NODE_OPTIONS=--no-deprecation pnpm --ignore-workspace install", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "lint:fix": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "reinstall": "cross-env NODE_OPTIONS=--no-deprecation rm -rf node_modules && rm pnpm-lock.yaml && pnpm --ignore-workspace install", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "prepare": "husky"}, "dependencies": {"@google-cloud/text-to-speech": "5.8.0", "@hookform/resolvers": "3.10.0", "@next/third-parties": "15.1.6", "@payloadcms/db-mongodb": "3.42.0", "@payloadcms/live-preview-react": "3.42.0", "@payloadcms/next": "3.42.0", "@payloadcms/payload-cloud": "3.42.0", "@payloadcms/plugin-form-builder": "3.42.0", "@payloadcms/plugin-nested-docs": "3.42.0", "@payloadcms/plugin-redirects": "3.42.0", "@payloadcms/plugin-search": "3.42.0", "@payloadcms/plugin-sentry": "3.42.0", "@payloadcms/plugin-seo": "3.42.0", "@payloadcms/richtext-lexical": "3.42.0", "@payloadcms/storage-s3": "3.42.0", "@payloadcms/ui": "3.42.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-avatar": "1.1.9", "@radix-ui/react-checkbox": "1.0.4", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-label": "2.0.2", "@radix-ui/react-popover": "1.1.6", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.2", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-tabs": "1.1.11", "@radix-ui/react-tooltip": "1.1.8", "@radix-ui/react-visually-hidden": "1.1.1", "@react-pdf/renderer": "4.3.0", "@sentry/nextjs": "9.0.1", "@tanstack/react-query": "5.62.15", "@tanstack/react-virtual": "3.13.8", "axios": "1.7.9", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "cmdk": "1.1.1", "cookie": "1.0.2", "cross-env": "7.0.3", "date-fns": "4.1.0", "dayjs": "1.11.13", "firebase": "11.1.0", "geist": "1.3.0", "google-auth-library": "^10.1.0", "graphql": "16.8.2", "html-to-image": "1.11.13", "immer": "10.1.1", "js-cookie": "3.0.5", "jsonwebtoken": "9.0.2", "jwks-rsa": "^3.2.0", "jwt-decode": "4.0.0", "limax": "4.1.0", "lodash-es": "4.17.21", "lucide-react": "0.378.0", "next": "15.1.6", "next-intl": "3.26.3", "next-sitemap": "4.2.3", "oauth4webapi": "3.1.4", "payload": "3.42.0", "payload-admin-bar": "1.0.6", "payload-oauth2": "1.0.9", "pdfjs-dist": "2.16.105", "photoswipe": "5.4.4", "prism-react-renderer": "2.3.1", "qs-esm": "7.0.2", "react": "19.0.0", "react-datepicker": "8.2.1", "react-day-picker": "8.10.1", "react-dom": "19.0.0", "react-google-recaptcha-v3": "1.10.1", "react-hook-form": "7.45.4", "react-infinite-scroll-component": "6.1.0", "react-joyride-react-19": "2.9.2", "react-markdown": "10.1.0", "react-photoswipe-gallery": "3.1.1", "react-rnd": "^10.5.2", "react-toastify": "11.0.3", "sharp": "0.32.6", "slugify": "1.6.6", "swiper": "11.2.0", "tailwind-merge": "2.3.0", "tailwindcss-animate": "1.0.7", "ua-parser-js": "2.0.0", "uuid": "11.0.5", "zod": "3.24.1", "zustand": "5.0.3"}, "devDependencies": {"@eslint/eslintrc": "3.2.0", "@tailwindcss/typography": "0.5.13", "@types/escape-html": "1.0.2", "@types/js-cookie": "3.0.6", "@types/jsonwebtoken": "9.0.9", "@types/lodash-es": "4.17.12", "@types/node": "22.5.4", "@types/react": "19.0.1", "@types/react-dom": "19.0.1", "autoprefixer": "10.4.19", "copyfiles": "2.4.1", "eslint": "9.16.0", "eslint-config-next": "15.1.0", "eslint-plugin-import": "^2.32.0", "husky": "9.1.7", "lint-staged": "15.5.1", "postcss": "8.4.38", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "0.6.9", "sass": "1.86.3", "tailwindcss": "3.4.3", "typescript": "5.7.2"}, "engines": {"node": "^18.20.2 || >=20.9.0"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.2", "@img/sharp-darwin-x64": "^0.33.2", "@img/sharp-linuxmusl-x64": "^0.33.2", "@img/sharp-win32-x64": "^0.33.2"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint", "prettier --write"], "*.{json,md,css,scss}": ["prettier --write"]}}